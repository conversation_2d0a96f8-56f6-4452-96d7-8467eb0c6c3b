'use client';

import { useState, useRef } from 'react';
import { X, Upload, Save, Trash2, Image as ImageIcon, Star } from 'lucide-react';

interface ProductImage {
  id: string;
  secure_url: string;
  alt_text: string;
  is_primary: boolean;
  order: number;
  file?: File; // For preview before upload
  uploading?: boolean;
}

interface CreateProductModalProps {
  onClose: () => void;
  onSuccess: () => void;
}

export default function CreateProductModal({ onClose, onSuccess }: CreateProductModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    shortDescription: '',
    price: '',
    comparePrice: '',
    categoryId: '',
    inventoryCount: '',
    status: 'active',
    featured: false,
  });
  const [images, setImages] = useState<ProductImage[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleNameChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      name,
      slug: generateSlug(name),
    }));
  };

  const handleImageUpload = async (files: FileList) => {
    const newImages: ProductImage[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setError('Please select only image files');
        continue;
      }
      
      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        setError('Image size must be less than 5MB');
        continue;
      }
      
      const tempId = `temp-${Date.now()}-${i}`;
      const imageUrl = URL.createObjectURL(file);
      
      newImages.push({
        id: tempId,
        secure_url: imageUrl,
        alt_text: `${formData.name || 'Product'} image ${images.length + i + 1}`,
        is_primary: images.length === 0 && i === 0, // First image is primary
        order: images.length + i,
        file,
        uploading: false,
      });
    }
    
    setImages(prev => [...prev, ...newImages]);
  };

  const uploadImageToCloudinary = async (image: ProductImage): Promise<ProductImage> => {
    if (!image.file) throw new Error('No file to upload');
    
    const formData = new FormData();
    formData.append('file', image.file);
    formData.append('folder', 'products');
    
    const response = await fetch('/api/upload/image', {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      const result = await response.json();
      throw new Error(result.error || 'Failed to upload image');
    }
    
    const result = await response.json();
    
    return {
      ...image,
      id: result.data.publicId,
      secure_url: result.data.secureUrl,
      file: undefined,
      uploading: false,
    };
  };

  const removeImage = (imageId: string) => {
    setImages(prev => {
      const filtered = prev.filter(img => img.id !== imageId);
      // If we removed the primary image, make the first remaining image primary
      if (filtered.length > 0 && !filtered.some(img => img.is_primary)) {
        filtered[0].is_primary = true;
      }
      return filtered;
    });
  };

  const setPrimaryImage = (imageId: string) => {
    setImages(prev => prev.map(img => ({
      ...img,
      is_primary: img.id === imageId
    })));
  };

  const updateImageAltText = (imageId: string, altText: string) => {
    setImages(prev => prev.map(img =>
      img.id === imageId ? { ...img, alt_text: altText } : img
    ));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Upload all images to Cloudinary first
      const uploadedImages: ProductImage[] = [];
      
      for (const image of images) {
        if (image.file) {
          // Update UI to show uploading state
          setImages(prev => prev.map(img =>
            img.id === image.id ? { ...img, uploading: true } : img
          ));
          
          try {
            const uploadedImage = await uploadImageToCloudinary(image);
            uploadedImages.push(uploadedImage);
            
            // Update UI with uploaded image
            setImages(prev => prev.map(img =>
              img.id === image.id ? uploadedImage : img
            ));
          } catch (uploadError) {
            console.error('Failed to upload image:', uploadError);
            setError(`Failed to upload image: ${image.alt_text}`);
            return;
          }
        } else {
          uploadedImages.push(image);
        }
      }

      const productData = {
        name: formData.name,
        slug: formData.slug,
        description: formData.description,
        shortDescription: formData.shortDescription,
        price: parseFloat(formData.price),
        comparePrice: formData.comparePrice ? parseFloat(formData.comparePrice) : undefined,
        categoryId: formData.categoryId || undefined,
        inventoryCount: parseInt(formData.inventoryCount) || 0,
        status: formData.status,
        featured: formData.featured,
        images: uploadedImages.map(img => ({
          id: img.id,
          secure_url: img.secure_url,
          alt_text: img.alt_text,
          is_primary: img.is_primary,
          order: img.order,
        })),
        tags: [],
      };

      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.error || 'Failed to create product');
      }

      onSuccess();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-charcoal rounded-lg border border-gold/20 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gold/20">
          <h2 className="text-xl font-semibold text-gold">Create New Product</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gold transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-900/20 border border-red-500 rounded-lg p-4">
              <p className="text-red-400">{error}</p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Product Name */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Product Name *
              </label>
              <input
                type="text"
                required
                value={formData.name}
                onChange={(e) => handleNameChange(e.target.value)}
                className="w-full px-4 py-2 bg-black border border-gold/20 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-gold"
                placeholder="Enter product name"
              />
            </div>

            {/* Slug */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                URL Slug *
              </label>
              <input
                type="text"
                required
                value={formData.slug}
                onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                className="w-full px-4 py-2 bg-black border border-gold/20 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-gold"
                placeholder="product-url-slug"
              />
            </div>

            {/* Price */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Price *
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                required
                value={formData.price}
                onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                className="w-full px-4 py-2 bg-black border border-gold/20 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-gold"
                placeholder="0.00"
              />
            </div>

            {/* Compare Price */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Compare Price
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.comparePrice}
                onChange={(e) => setFormData(prev => ({ ...prev, comparePrice: e.target.value }))}
                className="w-full px-4 py-2 bg-black border border-gold/20 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-gold"
                placeholder="0.00"
              />
            </div>

            {/* Inventory Count */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Inventory Count
              </label>
              <input
                type="number"
                min="0"
                value={formData.inventoryCount}
                onChange={(e) => setFormData(prev => ({ ...prev, inventoryCount: e.target.value }))}
                className="w-full px-4 py-2 bg-black border border-gold/20 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-gold"
                placeholder="0"
              />
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                className="w-full px-4 py-2 bg-black border border-gold/20 rounded-md text-white focus:outline-none focus:border-gold"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="out_of_stock">Out of Stock</option>
              </select>
            </div>

            {/* Short Description */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Short Description
              </label>
              <textarea
                rows={2}
                value={formData.shortDescription}
                onChange={(e) => setFormData(prev => ({ ...prev, shortDescription: e.target.value }))}
                className="w-full px-4 py-2 bg-black border border-gold/20 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-gold resize-none"
                placeholder="Brief product description"
              />
            </div>

            {/* Description */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Description
              </label>
              <textarea
                rows={4}
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-4 py-2 bg-black border border-gold/20 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-gold resize-none"
                placeholder="Detailed product description"
              />
            </div>

            {/* Image Upload Section */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Product Images
              </label>
              
              {/* Upload Area */}
              <div className="border-2 border-dashed border-gold/20 rounded-lg p-6 text-center hover:border-gold/40 transition-colors">
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => e.target.files && handleImageUpload(e.target.files)}
                  className="hidden"
                />
                <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400 mb-2">Drag and drop images here, or click to select</p>
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="btn-secondary inline-flex items-center"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Choose Images
                </button>
                <p className="text-xs text-gray-500 mt-2">
                  Supports: JPEG, PNG, WebP, GIF. Max size: 5MB per image.
                </p>
              </div>

              {/* Image Preview Grid */}
              {images.length > 0 && (
                <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4">
                  {images.map((image, index) => (
                    <div key={image.id} className="relative group">
                      <div className="aspect-square bg-black rounded-lg overflow-hidden border border-gold/20">
                        <img
                          src={image.secure_url}
                          alt={image.alt_text}
                          className="w-full h-full object-cover"
                        />
                        {image.uploading && (
                          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gold"></div>
                          </div>
                        )}
                      </div>
                      
                      {/* Image Controls */}
                      <div className="absolute top-2 right-2 flex space-x-1">
                        <button
                          type="button"
                          onClick={() => setPrimaryImage(image.id)}
                          className={`p-1 rounded ${
                            image.is_primary
                              ? 'bg-gold text-black'
                              : 'bg-black/50 text-white hover:bg-gold/20'
                          }`}
                          title="Set as primary image"
                        >
                          <Star className="w-3 h-3" />
                        </button>
                        <button
                          type="button"
                          onClick={() => removeImage(image.id)}
                          className="p-1 bg-red-500/80 text-white rounded hover:bg-red-500"
                          title="Remove image"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                      
                      {/* Primary Badge */}
                      {image.is_primary && (
                        <div className="absolute bottom-2 left-2 bg-gold text-black text-xs px-2 py-1 rounded">
                          Primary
                        </div>
                      )}
                      
                      {/* Alt Text Input */}
                      <input
                        type="text"
                        value={image.alt_text}
                        onChange={(e) => updateImageAltText(image.id, e.target.value)}
                        placeholder="Alt text"
                        className="mt-2 w-full px-2 py-1 text-xs bg-black border border-gold/20 rounded text-white placeholder-gray-400 focus:outline-none focus:border-gold"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Featured */}
            <div className="md:col-span-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.featured}
                  onChange={(e) => setFormData(prev => ({ ...prev, featured: e.target.checked }))}
                  className="w-4 h-4 text-gold bg-black border-gold/20 rounded focus:ring-gold focus:ring-2"
                />
                <span className="ml-2 text-sm text-gray-300">Featured Product</span>
              </label>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gold/20">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center"
            >
              <Save className="w-4 h-4 mr-2" />
              {loading ? 'Creating...' : 'Create Product'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}