'use client';

import { useState } from 'react';
import { Plus, X } from 'lucide-react';

interface SizeSelectorProps {
  sizes: string[];
  onChange: (sizes: string[]) => void;
  error?: string;
}

// Common size presets for quick selection
const SIZE_PRESETS = {
  clothing: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
  shoes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
  numeric: ['28', '30', '32', '34', '36', '38', '40', '42', '44', '46'],
};

export default function SizeSelector({ sizes, onChange, error }: SizeSelectorProps) {
  const [newSize, setNewSize] = useState('');
  const [selectedPreset, setSelectedPreset] = useState<keyof typeof SIZE_PRESETS | ''>('');

  const addSize = () => {
    if (newSize.trim() && !sizes.includes(newSize.trim())) {
      onChange([...sizes, newSize.trim()]);
      setNewSize('');
    }
  };

  const removeSize = (sizeToRemove: string) => {
    onChange(sizes.filter(size => size !== sizeToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addSize();
    }
  };

  const applyPreset = (presetKey: keyof typeof SIZE_PRESETS) => {
    const presetSizes = SIZE_PRESETS[presetKey];
    // Add only sizes that aren't already selected
    const newSizes = presetSizes.filter(size => !sizes.includes(size));
    onChange([...sizes, ...newSizes]);
    setSelectedPreset('');
  };

  const addPresetSize = (size: string) => {
    if (!sizes.includes(size)) {
      onChange([...sizes, size]);
    }
  };

  return (
    <div>
      <label className="block text-sm font-medium text-gray-300 mb-2">
        Sizes *
      </label>
      
      {/* Size presets */}
      <div className="mb-3">
        <p className="text-xs text-gray-400 mb-2">Quick add common sizes:</p>
        <div className="flex flex-wrap gap-2 mb-2">
          <button
            type="button"
            onClick={() => applyPreset('clothing')}
            className="px-3 py-1 text-xs bg-charcoal border border-gold/20 rounded text-gray-300 hover:bg-gold/10 hover:text-gold focus:outline-none"
          >
            Clothing (XS-XXL)
          </button>
          <button
            type="button"
            onClick={() => applyPreset('shoes')}
            className="px-3 py-1 text-xs bg-charcoal border border-gold/20 rounded text-gray-300 hover:bg-gold/10 hover:text-gold focus:outline-none"
          >
            Shoes (6-12)
          </button>
          <button
            type="button"
            onClick={() => applyPreset('numeric')}
            className="px-3 py-1 text-xs bg-charcoal border border-gold/20 rounded text-gray-300 hover:bg-gold/10 hover:text-gold focus:outline-none"
          >
            Numeric (28-46)
          </button>
        </div>
      </div>

      {/* Add custom size input */}
      <div className="flex gap-2 mb-3">
        <input
          type="text"
          value={newSize}
          onChange={(e) => setNewSize(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Enter custom size (e.g., XL, 42, 10.5)"
          className="flex-1 px-4 py-2 bg-black border border-gold/20 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-gold"
        />
        <button
          type="button"
          onClick={addSize}
          disabled={!newSize.trim() || sizes.includes(newSize.trim())}
          className="px-4 py-2 bg-gold text-black rounded-md hover:bg-gold/90 focus:outline-none focus:ring-2 focus:ring-gold/50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
        >
          <Plus className="w-4 h-4" />
          Add
        </button>
      </div>

      {/* Display selected sizes */}
      {sizes.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-2">
          {sizes.map((size, index) => (
            <div
              key={index}
              className="flex items-center gap-2 px-3 py-1 bg-charcoal border border-gold/20 rounded-md text-sm text-white"
            >
              <span>{size}</span>
              <button
                type="button"
                onClick={() => removeSize(size)}
                className="text-gray-400 hover:text-red-400 focus:outline-none"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Validation message */}
      {error && (
        <p className="text-red-400 text-sm mt-1">{error}</p>
      )}
      
      {sizes.length === 0 && !error && (
        <p className="text-gray-400 text-sm mt-1">
          Add at least one size option for this product
        </p>
      )}
    </div>
  );
}
