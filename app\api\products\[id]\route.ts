// Individual product API routes
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';
import { db } from '@/lib/database/index';
import { z } from 'zod';

const updateProductSchema = z.object({
  name: z.string().min(1, 'Product name is required').optional(),
  slug: z.string().min(1, 'Product slug is required').optional(),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  price: z.number().min(0, 'Price must be positive').optional(),
  comparePrice: z.number().min(0).optional(),
  costPrice: z.number().min(0).optional(),
  categoryId: z.string().optional(),
  inventoryCount: z.number().min(0).optional(),
  trackInventory: z.boolean().optional(),
  allowBackorder: z.boolean().optional(),
  weight: z.number().min(0).optional(),
  dimensions: z.object({
    length: z.number().min(0),
    width: z.number().min(0),
    height: z.number().min(0),
  }).optional(),
  images: z.array(z.object({
    id: z.string(),
    secure_url: z.string(),
    alt_text: z.string(),
    is_primary: z.boolean(),
    order: z.number(),
  })).optional(),
  tags: z.array(z.string()).optional(),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  status: z.enum(['active', 'inactive', 'out_of_stock']).optional(),
  featured: z.boolean().optional(),
});

async function getProduct(request: NextRequest, context: { params: { id: string } }) {
  const { params } = context;
  const productId = params.id;
  
  try {
    const result = await db.getProduct(productId);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 404 }
      );
    }

    return NextResponse.json({ data: result.data });
  } catch (error) {
    console.error('Get product error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch product' },
      { status: 500 }
    );
  }
}

async function updateProduct(request: NextRequest, context: { params: { id: string } }) {
  const { params } = context;
  const productId = params.id;
  
  try {
    const body = await request.json();
    const validationResult = updateProductSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const updateData: any = {};
    if (validationResult.data.name) updateData.name = validationResult.data.name;
    if (validationResult.data.slug) updateData.slug = validationResult.data.slug;
    if (validationResult.data.description !== undefined) updateData.description = validationResult.data.description;
    if (validationResult.data.shortDescription !== undefined) updateData.short_description = validationResult.data.shortDescription;
    if (validationResult.data.price !== undefined) updateData.price = validationResult.data.price;
    if (validationResult.data.comparePrice !== undefined) updateData.compare_price = validationResult.data.comparePrice;
    if (validationResult.data.costPrice !== undefined) updateData.cost_price = validationResult.data.costPrice;
    if (validationResult.data.categoryId !== undefined) updateData.category_id = validationResult.data.categoryId;
    if (validationResult.data.inventoryCount !== undefined) updateData.inventory_count = validationResult.data.inventoryCount;
    if (validationResult.data.trackInventory !== undefined) updateData.track_inventory = validationResult.data.trackInventory;
    if (validationResult.data.allowBackorder !== undefined) updateData.allow_backorder = validationResult.data.allowBackorder;
    if (validationResult.data.weight !== undefined) updateData.weight = validationResult.data.weight;
    if (validationResult.data.dimensions !== undefined) updateData.dimensions = validationResult.data.dimensions;
    if (validationResult.data.images !== undefined) updateData.images = validationResult.data.images;
    if (validationResult.data.tags !== undefined) updateData.tags = validationResult.data.tags;
    if (validationResult.data.metaTitle !== undefined) updateData.meta_title = validationResult.data.metaTitle;
    if (validationResult.data.metaDescription !== undefined) updateData.meta_description = validationResult.data.metaDescription;
    if (validationResult.data.status !== undefined) updateData.status = validationResult.data.status;
    if (validationResult.data.featured !== undefined) updateData.featured = validationResult.data.featured;

    const result = await db.updateProduct(productId, updateData);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      message: 'Product updated successfully',
      data: result.data 
    });
  } catch (error) {
    console.error('Update product error:', error);
    return NextResponse.json(
      { error: 'Failed to update product' },
      { status: 500 }
    );
  }
}

async function deleteProduct(request: NextRequest, context: { params: { id: string } }) {
  const { params } = context;
  const productId = params.id;
  
  try {
    const result = await db.deleteProduct(productId);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Delete product error:', error);
    return NextResponse.json(
      { error: 'Failed to delete product' },
      { status: 500 }
    );
  }
}

export const GET = getProduct; // Public endpoint
export const PUT = withAuth(updateProduct, { requireAdmin: true });
export const DELETE = withAuth(deleteProduct, { requireAdmin: true });